<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>We Are One</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: background-color 0.5s ease;
        }

        .controls {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .speed-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .speed-control label {
            font-weight: bold;
            font-family: Arial, sans-serif;
            white-space: nowrap;
        }

        .speed-control input {
            width: 80px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
            font-family: Arial, sans-serif;
        }



        #toggleBtn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-family: Arial, sans-serif;
        }

        #toggleBtn:hover {
            background: #0056b3;
        }

        .centered-text {
            position: relative;
            font-size: 6rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            white-space: nowrap;
        }

        .letter {
            position: absolute;
            transition: color 0.5s ease, font-family 0.5s ease;
        }

        @media (max-width: 768px) {
            .centered-text {
                font-size: 3rem;
            }
            .letter {
                font-size: 3rem;
            }
            .controls {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="controls">
        <div class="speed-control">
            <label for="speed">Geschwindigkeit:</label>
            <input type="number" id="speed" min="0" max="9999" value="1000">
        </div>
        <button id="toggleBtn">Stoppen</button>
    </div>

    <div class="centered-text" id="text"></div>

    <script>
        const fonts = [
            'Arial, sans-serif',
            'Helvetica, sans-serif',
            'Times New Roman, serif',
            'Georgia, serif',
            'Verdana, sans-serif',
            'Comic Sans MS, cursive',
            'Impact, sans-serif',
            'Trebuchet MS, sans-serif',
            'Courier New, monospace',
            'Palatino, serif',
            'Tahoma, sans-serif',
            'Lucida Console, monospace'
        ];

        // Funktion für zufällige Farben
        function getRandomColor() {
            return '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
        }

        function getRandomBackgroundColor() {
            return '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
        }

        let currentFontIndex = 0;
        let animationInterval;
        let isRunning = true;
        let speed = 1000;
        let letterElements = [];
        let letterPositions = [];

        const textElement = document.getElementById('text');
        const speedInput = document.getElementById('speed');
        const toggleBtn = document.getElementById('toggleBtn');
        const textContent = "we are one";

        // Initialisierung der Buchstaben mit festen Positionen
        function initializeLetters() {
            textElement.innerHTML = '';
            letterElements = [];
            letterPositions = [];

            // Temporäres Element für Messungen mit Arial
            const measureElement = document.createElement('div');
            measureElement.style.position = 'absolute';
            measureElement.style.visibility = 'hidden';
            measureElement.style.fontFamily = 'Arial, sans-serif';
            measureElement.style.fontSize = '6rem';
            measureElement.style.fontWeight = '700';
            measureElement.style.whiteSpace = 'nowrap';
            document.body.appendChild(measureElement);

            let totalWidth = 0;

            // Berechne die Position jedes Buchstabens basierend auf Arial
            for (let i = 0; i < textContent.length; i++) {
                const char = textContent[i];
                measureElement.textContent = char;
                const charWidth = measureElement.offsetWidth;

                letterPositions.push({
                    left: totalWidth,
                    width: charWidth,
                    char: char
                });

                totalWidth += charWidth;
            }

            document.body.removeChild(measureElement);

            // Zentriere den gesamten Text
            const containerWidth = totalWidth;
            const startOffset = -containerWidth / 2;

            // Erstelle die Buchstaben-Elemente
            for (let i = 0; i < textContent.length; i++) {
                const letterSpan = document.createElement('span');
                letterSpan.className = 'letter';
                letterSpan.textContent = letterPositions[i].char;
                letterSpan.style.left = (startOffset + letterPositions[i].left) + 'px';
                letterSpan.style.fontFamily = 'Arial, sans-serif';
                letterSpan.style.fontSize = '6rem';
                letterSpan.style.fontWeight = '700';

                textElement.appendChild(letterSpan);
                letterElements.push(letterSpan);
            }
        }

        function updateStyles() {
            // Ändere Schriftart für alle Buchstaben
            const currentFont = fonts[currentFontIndex];
            letterElements.forEach(letter => {
                letter.style.fontFamily = currentFont;
                letter.style.color = getRandomColor(); // Jeder Buchstabe bekommt eine zufällige Farbe
            });

            // Zufällige Hintergrundfarbe
            document.body.style.backgroundColor = getRandomBackgroundColor();

            currentFontIndex = (currentFontIndex + 1) % fonts.length;
        }

        function startAnimation() {
            if (animationInterval) clearInterval(animationInterval);
            animationInterval = setInterval(updateStyles, speed);
        }

        function stopAnimation() {
            if (animationInterval) {
                clearInterval(animationInterval);
                animationInterval = null;
            }
        }

        speedInput.addEventListener('input', function() {
            let inputValue = parseInt(this.value);
            if (isNaN(inputValue) || inputValue < 0) {
                inputValue = 0;
            } else if (inputValue > 9999) {
                inputValue = 9999;
            }

            // Je höher der Wert, desto schneller (umgekehrte Logik)
            // 0 = sehr langsam (5000ms), 9999 = sehr schnell (10ms)
            speed = Math.max(10, 5000 - (inputValue * 0.5));

            if (isRunning) {
                startAnimation();
            }
        });

        toggleBtn.addEventListener('click', function() {
            if (isRunning) {
                stopAnimation();
                this.textContent = 'Starten';
                isRunning = false;
            } else {
                startAnimation();
                this.textContent = 'Stoppen';
                isRunning = true;
            }
        });

        // Initial setup
        initializeLetters();
        updateStyles();
        startAnimation();
    </script>
</body>
</html>
